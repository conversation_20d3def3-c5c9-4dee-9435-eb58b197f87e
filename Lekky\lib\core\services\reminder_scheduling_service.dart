import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../utils/reminder_calculator.dart';
import '../constants/preference_keys.dart';
import '../di/service_locator.dart';
import 'reminder_content_generator.dart';
import '../../features/notifications/data/notification_service.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/domain/repositories/notification_repository.dart';

/// Service for managing automatic reminder scheduling
class ReminderSchedulingService {
  static final ReminderSchedulingService _instance =
      ReminderSchedulingService._internal();

  factory ReminderSchedulingService() => _instance;
  ReminderSchedulingService._internal();

  static const String _scheduledRemindersKey = 'scheduled_reminder_ids';
  static const int _maxScheduledReminders = 1;

  /// Schedule reminders based on current settings
  Future<void> scheduleReminders() async {
    try {
      final settings = await _loadReminderSettings();

      if (!settings['enabled'] || settings['startDateTime'] == null) {
        await cancelAllReminders();
        return;
      }

      // Cancel existing reminders first
      await cancelAllReminders();

      // Calculate next reminder dates
      final reminderDates = ReminderCalculator.calculateNextReminders(
        startDateTime: settings['startDateTime'],
        frequency: settings['frequency'],
        count: _maxScheduledReminders,
      );

      // Schedule new reminders
      final scheduledIds = <int>[];
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();
      final contentGenerator = ReminderContentGenerator();

      for (final reminderDate in reminderDates) {
        final content = await contentGenerator.generateContextualContent(
          settings['frequency'],
        );

        final notification = AppNotification(
          title: content['title']!,
          message: content['message']!,
          timestamp: reminderDate,
          type: NotificationType.readingReminder,
        );

        try {
          await notificationService.scheduleNotification(
              notification, reminderDate);
          if (notification.id != null) {
            scheduledIds.add(notification.id!);
          }
        } catch (e) {
          Logger.error('Failed to schedule individual reminder: $e');
        }
      }

      // Store scheduled IDs for future cancellation
      await _storeScheduledReminderIds(scheduledIds);

      Logger.info('Scheduled ${scheduledIds.length} reminders');
    } catch (e) {
      Logger.error('Error scheduling reminders: $e');
    }
  }

  /// Cancel all scheduled reminders
  Future<void> cancelAllReminders() async {
    try {
      final scheduledIds = await _getScheduledReminderIds();

      if (scheduledIds.isNotEmpty) {
        final notificationService =
            await serviceLocator.getAsync<NotificationService>();

        for (final id in scheduledIds) {
          try {
            await notificationService.cancelNotification(id);
          } catch (e) {
            Logger.error('Failed to cancel reminder $id: $e');
          }
        }

        // Clear stored IDs
        await _clearScheduledReminderIds();

        Logger.info('Cancelled ${scheduledIds.length} scheduled reminders');
      }
    } catch (e) {
      Logger.error('Error cancelling reminders: $e');
    }
  }

  /// Update reminders when settings change
  Future<void> updateReminders() async {
    await scheduleReminders();
  }

  /// Handle reminder firing and auto-reschedule next one
  Future<void> onReminderFired() async {
    try {
      Logger.info(
          'ReminderSchedulingService: Reminder fired, auto-rescheduling next one');

      // Auto-dismiss current reminder by clearing it from app notifications
      await _dismissCurrentReminder();

      // Schedule the next reminder
      await scheduleReminders();

      Logger.info(
          'ReminderSchedulingService: Successfully auto-rescheduled next reminder');
    } catch (e) {
      Logger.error(
          'ReminderSchedulingService: Failed to auto-reschedule reminder: $e');
    }
  }

  /// Dismiss current reminder from app notifications
  Future<void> _dismissCurrentReminder() async {
    try {
      // Get notification repository directly from service locator
      final notificationRepository =
          await serviceLocator.getAsync<NotificationRepository>();
      await notificationRepository
          .deleteNotificationsByType(NotificationType.readingReminder);

      Logger.info(
          'ReminderSchedulingService: Dismissed current reminder from app notifications');
    } catch (e) {
      Logger.error(
          'ReminderSchedulingService: Failed to dismiss current reminder: $e');
    }
  }

  /// Check if reminders should be active based on settings
  Future<bool> shouldRemindersBeActive() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remindersEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;
      final startDateTime =
          prefs.getString(PreferenceKeys.reminderStartDateTime);

      return remindersEnabled && startDateTime != null;
    } catch (e) {
      Logger.error('Error checking reminder status: $e');
      return false;
    }
  }

  /// Get next few reminder dates for UI display
  Future<List<DateTime>> getUpcomingReminders({int count = 3}) async {
    try {
      final settings = await _loadReminderSettings();

      if (!settings['enabled'] || settings['startDateTime'] == null) {
        return [];
      }

      return ReminderCalculator.calculateNextReminders(
        startDateTime: settings['startDateTime'],
        frequency: settings['frequency'],
        count: count,
      );
    } catch (e) {
      Logger.error('Error getting upcoming reminders: $e');
      return [];
    }
  }

  /// Load reminder settings from preferences
  Future<Map<String, dynamic>> _loadReminderSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final enabled = prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;
      final frequency =
          prefs.getString(PreferenceKeys.reminderFrequency) ?? 'weekly';
      final startDateTimeString =
          prefs.getString(PreferenceKeys.reminderStartDateTime);

      DateTime? startDateTime;
      if (startDateTimeString != null) {
        try {
          startDateTime = DateTime.parse(startDateTimeString);
        } catch (e) {
          Logger.error('Error parsing reminder start date: $e');
        }
      }

      return {
        'enabled': enabled,
        'frequency': frequency,
        'startDateTime': startDateTime,
      };
    } catch (e) {
      Logger.error('Error loading reminder settings: $e');
      return {
        'enabled': false,
        'frequency': 'weekly',
        'startDateTime': null,
      };
    }
  }

  /// Store scheduled reminder IDs
  Future<void> _storeScheduledReminderIds(List<int> ids) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final idsString = ids.map((id) => id.toString()).join(',');
      await prefs.setString(_scheduledRemindersKey, idsString);
    } catch (e) {
      Logger.error('Error storing scheduled reminder IDs: $e');
    }
  }

  /// Get scheduled reminder IDs
  Future<List<int>> _getScheduledReminderIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final idsString = prefs.getString(_scheduledRemindersKey);

      if (idsString == null || idsString.isEmpty) return [];

      return idsString
          .split(',')
          .where((s) => s.isNotEmpty)
          .map((s) => int.tryParse(s))
          .where((id) => id != null)
          .cast<int>()
          .toList();
    } catch (e) {
      Logger.error('Error getting scheduled reminder IDs: $e');
      return [];
    }
  }

  /// Clear scheduled reminder IDs
  Future<void> _clearScheduledReminderIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_scheduledRemindersKey);
    } catch (e) {
      Logger.error('Error clearing scheduled reminder IDs: $e');
    }
  }
}
