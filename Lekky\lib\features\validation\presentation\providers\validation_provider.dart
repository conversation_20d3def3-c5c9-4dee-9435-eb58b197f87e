import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../controllers/validation_dashboard_controller.dart';
import '../../domain/models/validation_issue.dart';
import '../../domain/models/integrity_report.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/utils/event_bus.dart';

/// Provider for validation dashboard controller
final validationControllerProvider =
    StateNotifierProvider<ValidationNotifier, ValidationState>((ref) {
  final controller = serviceLocator<ValidationDashboardController>();
  return ValidationNotifier(controller);
});

/// Validation state
class ValidationState {
  final List<ValidationIssue> issues;
  final List<ValidationIssue> filteredIssues;
  final IntegrityReport? integrityReport;
  final ValidationIssueFilterType filterType;
  final bool isLoading;
  final String? errorMessage;

  const ValidationState({
    this.issues = const [],
    this.filteredIssues = const [],
    this.integrityReport,
    this.filterType = ValidationIssueFilterType.all,
    this.isLoading = false,
    this.errorMessage,
  });

  ValidationState copyWith({
    List<ValidationIssue>? issues,
    List<ValidationIssue>? filteredIssues,
    IntegrityReport? integrityReport,
    ValidationIssueFilterType? filterType,
    bool? isLoading,
    String? errorMessage,
    bool clearError = false,
  }) {
    return ValidationState(
      issues: issues ?? this.issues,
      filteredIssues: filteredIssues ?? this.filteredIssues,
      integrityReport: integrityReport ?? this.integrityReport,
      filterType: filterType ?? this.filterType,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
    );
  }
}

/// Validation state notifier
class ValidationNotifier extends StateNotifier<ValidationState> {
  final ValidationDashboardController _controller;
  StreamSubscription<EventType>? _eventSubscription;

  ValidationNotifier(this._controller)
      : super(const ValidationState(isLoading: true)) {
    _controller.addListener(_onControllerChanged);
    _setupEventListener();
    _initialize();
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _eventSubscription?.cancel();
    super.dispose();
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel();
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.dataUpdated) {
        // Refresh validation when data is updated
        refresh();
      }
    });
  }

  /// Initialize the validation data
  Future<void> _initialize() async {
    try {
      await _controller.initialize();
      // Update state after initialization completes
      _onControllerChanged();
    } catch (e) {
      // Handle initialization error
      state = ValidationState(
        isLoading: false,
        errorMessage: 'Failed to initialize validation data: $e',
      );
    }
  }

  /// Handle controller changes
  void _onControllerChanged() {
    state = ValidationState(
      issues: _controller.issues,
      filteredIssues: _controller.filteredIssues,
      integrityReport: _controller.integrityReport,
      filterType: _controller.filterType,
      isLoading: _controller.isLoading,
      errorMessage: _controller.errorMessage,
    );
  }

  /// Set filter type
  void setFilterType(ValidationIssueFilterType filterType) {
    _controller.setFilterType(filterType);
  }

  /// Clear all filters
  void clearFilters() {
    _controller.clearFilters();
  }

  /// Refresh data
  Future<void> refresh() async {
    await _controller.refresh();
  }

  /// Get entry for issue
  Future<dynamic> getEntryForIssue(ValidationIssue issue) async {
    return await _controller.getEntryForIssue(issue);
  }
}
