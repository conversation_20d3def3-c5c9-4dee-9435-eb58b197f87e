import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../controllers/validation_dashboard_controller.dart';
import '../../domain/models/validation_issue.dart';
import '../../domain/models/integrity_report.dart';
import '../../domain/services/data_integrity_service.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/logger.dart';

/// Provider for validation dashboard controller
final validationControllerProvider =
    StateNotifierProvider<ValidationNotifier, ValidationState>((ref) {
  final controller = serviceLocator<ValidationDashboardController>();
  return ValidationNotifier(controller);
});

/// Validation state
class ValidationState {
  final List<ValidationIssue> issues;
  final List<ValidationIssue> filteredIssues;
  final IntegrityReport? integrityReport;
  final ValidationIssueFilterType filterType;
  final bool isLoading;
  final String? errorMessage;

  const ValidationState({
    this.issues = const [],
    this.filteredIssues = const [],
    this.integrityReport,
    this.filterType = ValidationIssueFilterType.all,
    this.isLoading = false,
    this.errorMessage,
  });

  ValidationState copyWith({
    List<ValidationIssue>? issues,
    List<ValidationIssue>? filteredIssues,
    IntegrityReport? integrityReport,
    ValidationIssueFilterType? filterType,
    bool? isLoading,
    String? errorMessage,
    bool clearError = false,
  }) {
    return ValidationState(
      issues: issues ?? this.issues,
      filteredIssues: filteredIssues ?? this.filteredIssues,
      integrityReport: integrityReport ?? this.integrityReport,
      filterType: filterType ?? this.filterType,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
    );
  }
}

/// Validation state notifier
class ValidationNotifier extends StateNotifier<ValidationState> {
  final ValidationDashboardController _controller;
  StreamSubscription<EventType>? _eventSubscription;

  ValidationNotifier(this._controller)
      : super(const ValidationState(isLoading: true)) {
    _controller.addListener(_onControllerChanged);
    _setupEventListener();
    _initialize();
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _eventSubscription?.cancel();
    super.dispose();
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel();
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.dataUpdated) {
        // Refresh validation when data is updated
        refresh();
      }
    });
  }

  /// Initialize the validation data
  Future<void> _initialize() async {
    try {
      await _controller.initialize();
      // Update state after initialization completes
      _onControllerChanged();
    } catch (e) {
      // Handle initialization error
      state = ValidationState(
        isLoading: false,
        errorMessage: 'Failed to initialize validation data: $e',
      );
    }
  }

  /// Handle controller changes
  void _onControllerChanged() {
    state = ValidationState(
      issues: _controller.issues,
      filteredIssues: _controller.filteredIssues,
      integrityReport: _controller.integrityReport,
      filterType: _controller.filterType,
      isLoading: _controller.isLoading,
      errorMessage: _controller.errorMessage,
    );
  }

  /// Set filter type
  void setFilterType(ValidationIssueFilterType filterType) {
    _controller.setFilterType(filterType);
  }

  /// Clear all filters
  void clearFilters() {
    _controller.clearFilters();
  }

  /// Refresh data
  Future<void> refresh() async {
    await _controller.refresh();
  }

  /// Get entry for issue
  Future<dynamic> getEntryForIssue(ValidationIssue issue) async {
    return await _controller.getEntryForIssue(issue);
  }
}

/// Pure Riverpod AsyncNotifier for validation dashboard
class RiverpodValidationNotifier extends AsyncNotifier<ValidationState> {
  final logger = Logger('RiverpodValidationNotifier');
  StreamSubscription<EventType>? _eventSubscription;

  @override
  Future<ValidationState> build() async {
    // Set up event listener for data updates
    _setupEventListener();

    // Load initial data
    return await _loadValidationData();
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel();
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.dataUpdated) {
        // Refresh validation when data is updated
        refresh();
      }
    });
  }

  /// Load validation data
  Future<ValidationState> _loadValidationData() async {
    try {
      final dataIntegrityService = serviceLocator<DataIntegrityService>();

      // Run integrity check
      final integrityReport = await dataIntegrityService.checkIntegrity();
      final issues = integrityReport.issues;

      // Apply default filter (all)
      final filteredIssues =
          _applyFilter(issues, ValidationIssueFilterType.all);

      return ValidationState(
        issues: issues,
        filteredIssues: filteredIssues,
        integrityReport: integrityReport,
        filterType: ValidationIssueFilterType.all,
        isLoading: false,
        errorMessage: null,
      );
    } catch (e) {
      logger.e('Failed to load validation issues', details: e.toString());
      return ValidationState(
        isLoading: false,
        errorMessage: 'Failed to load validation issues: $e',
      );
    }
  }

  /// Apply filter to issues
  List<ValidationIssue> _applyFilter(
      List<ValidationIssue> issues, ValidationIssueFilterType filterType) {
    switch (filterType) {
      case ValidationIssueFilterType.all:
        return List.from(issues);
      case ValidationIssueFilterType.highSeverity:
        return issues
            .where((issue) => issue.severity == ValidationIssueSeverity.high)
            .toList();
      case ValidationIssueFilterType.mediumSeverity:
        return issues
            .where((issue) => issue.severity == ValidationIssueSeverity.medium)
            .toList();
      case ValidationIssueFilterType.lowSeverity:
        return issues
            .where((issue) => issue.severity == ValidationIssueSeverity.low)
            .toList();
    }
  }

  /// Set filter type
  Future<void> setFilterType(ValidationIssueFilterType filterType) async {
    final currentState = await future;
    if (currentState.filterType == filterType) return;

    final filteredIssues = _applyFilter(currentState.issues, filterType);

    state = AsyncValue.data(currentState.copyWith(
      filterType: filterType,
      filteredIssues: filteredIssues,
    ));
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    await setFilterType(ValidationIssueFilterType.all);
  }

  /// Refresh validation data
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadValidationData());
  }

  /// Get entry for issue
  Future<dynamic> getEntryForIssue(ValidationIssue issue) async {
    if (issue.entryId == null) {
      return null;
    }

    try {
      final meterReadingRepository = serviceLocator<MeterReadingRepository>();
      final topUpRepository = serviceLocator<TopUpRepository>();

      // Try to get meter reading
      final meterReading =
          await meterReadingRepository.getMeterReadingById(issue.entryId!);
      if (meterReading != null) {
        return meterReading;
      }

      // Try to get top-up
      final topUp = await topUpRepository.getTopUpById(issue.entryId!);
      if (topUp != null) {
        return topUp;
      }

      return null;
    } catch (e) {
      logger.e('Failed to get entry for issue', details: e.toString());
      return null;
    }
  }

  void cleanup() {
    _eventSubscription?.cancel();
  }
}

/// Provider for the new Riverpod validation notifier
final riverpodValidationProvider =
    AsyncNotifierProvider<RiverpodValidationNotifier, ValidationState>(() {
  return RiverpodValidationNotifier();
});
