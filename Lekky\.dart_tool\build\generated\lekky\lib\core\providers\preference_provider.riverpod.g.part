// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$preferencesHash() => r'966b522116d15669938b18a5398fce603bc6d81d';

/// Preferences provider using AsyncNotifier for async initialization
///
/// Copied from [Preferences].
@ProviderFor(Preferences)
final preferencesProvider =
    AutoDisposeAsyncNotifierProvider<Preferences, PreferenceState>.internal(
  Preferences.new,
  name: r'preferencesProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$preferencesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Preferences = AutoDisposeAsyncNotifier<PreferenceState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
