// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$historyHash() => r'3976bb53290c09fb412a1570321393cd94f33aa5';

/// History provider with smart pagination and auto-refresh on data updates
///
/// Copied from [History].
@ProviderFor(History)
final historyProvider =
    AutoDisposeAsyncNotifierProvider<History, HistoryState>.internal(
  History.new,
  name: r'historyProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$historyHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$History = AutoDisposeAsyncNotifier<HistoryState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
