["freezed on lib/main.dart", ["", "This builder requires Dart inputs without syntax errors.\nHowever, package:lekky/main.dart (or an existing part) contains the following errors.\nmain.dart:218:17: Expected to find ';'.\nmain.dart:218:18: Expected an identifier.\nmain.dart:218:18: Unexpected text ';'.\nAnd 8 more...\n\nTry fixing the errors and re-running the build.\n", "#0      AnalyzerResolver.compilationUnitFor.<anonymous closure> (package:build_resolvers/src/resolver.dart:238:9)\n#1      Pool.withResource (package:pool/pool.dart:127:28)\n<asynchronous suspension>\n#2      _hasAnyTopLevelAnnotations (package:source_gen/src/builder.dart:363:18)\n<asynchronous suspension>\n#3      _Builder.build (package:source_gen/src/builder.dart:89:11)\n<asynchronous suspension>\n#4      runBuilder.buildForInput (package:build/src/generate/run_builder.dart:83:7)\n<asynchronous suspension>\n#5      Future.wait.<anonymous closure> (dart:async/future.dart:518:21)\n<asynchronous suspension>\n#6      scopeLogAsync.<anonymous closure> (package:build/src/builder/logging.dart:32:40)\n<asynchronous suspension>\n"]]