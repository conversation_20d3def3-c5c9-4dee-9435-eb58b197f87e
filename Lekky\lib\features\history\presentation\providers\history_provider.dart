import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/providers/database_provider.dart';
import '../../../../core/providers/error_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../domain/models/history_state.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';

part 'history_provider.g.dart';

/// History provider with smart pagination and auto-refresh on data updates
@riverpod
class History extends _$History {
  StreamSubscription<EventType>? _eventSubscription;

  /// Check if any filters are active (non-default)
  bool hasActiveFilters(HistoryState state) {
    return state.filterType != EntryFilterType.all ||
        state.sortOrder != EntrySortOrder.newestFirst ||
        state.startDate != null ||
        state.endDate != null;
  }

  @override
  Future<HistoryState> build() async {
    // Set up event listener for data updates
    _setupEventListener();

    // Dispose event subscription when provider is disposed
    ref.onDispose(() {
      _eventSubscription?.cancel();
    });

    return await _loadInitialState();
  }

  /// Load initial state
  Future<HistoryState> _loadInitialState() async {
    try {
      final initialState = HistoryState.initial();
      await _loadEntries(initialState);
      return state.value ?? initialState;
    } catch (error, stackTrace) {
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'History initialization',
          );
      return HistoryState.initial().copyWith(
        errorMessage: 'Failed to load history: ${error.toString()}',
      );
    }
  }

  /// Set filter type and reload entries
  Future<void> setFilterType(EntryFilterType filterType) async {
    final currentState = state.value ?? HistoryState.initial();
    if (currentState.filterType == filterType) return;

    state = AsyncValue.data(currentState.copyWith(
      filterType: filterType,
      currentPage: 0, // Reset to first page
      isLoading: true,
    ));

    await _loadEntries(state.value!);
  }

  /// Set sort order and reload entries
  Future<void> setSortOrder(EntrySortOrder sortOrder) async {
    final currentState = state.value ?? HistoryState.initial();
    if (currentState.sortOrder == sortOrder) return;

    state = AsyncValue.data(currentState.copyWith(
      sortOrder: sortOrder,
      isLoading: true,
    ));

    await _loadEntries(state.value!);
  }

  /// Set date range filter and reload entries
  Future<void> setDateRange(DateTime? startDate, DateTime? endDate) async {
    final currentState = state.value ?? HistoryState.initial();
    if (currentState.startDate == startDate &&
        currentState.endDate == endDate) {
      return;
    }

    state = AsyncValue.data(currentState.copyWith(
      startDate: startDate,
      endDate: endDate,
      currentPage: 0, // Reset to first page
      isLoading: true,
    ));

    await _loadEntries(state.value!);
  }

  /// Go to next page
  Future<void> nextPage() async {
    final currentState = state.value ?? HistoryState.initial();
    if (!currentState.canGoNext) return;

    state = AsyncValue.data(currentState.copyWith(
      currentPage: currentState.currentPage + 1,
      isLoading: true,
    ));

    await _loadEntries(state.value!);
  }

  /// Go to previous page
  Future<void> previousPage() async {
    final currentState = state.value ?? HistoryState.initial();
    if (!currentState.canGoPrevious) return;

    state = AsyncValue.data(currentState.copyWith(
      currentPage: currentState.currentPage - 1,
      isLoading: true,
    ));

    await _loadEntries(state.value!);
  }

  /// Go to specific page
  Future<void> goToPage(int page) async {
    final currentState = state.value ?? HistoryState.initial();
    if (page < 0 ||
        page >= currentState.totalPages ||
        page == currentState.currentPage) return;

    state = AsyncValue.data(currentState.copyWith(
      currentPage: page,
      isLoading: true,
    ));

    await _loadEntries(state.value!);
  }

  /// Go to first page
  Future<void> goToFirstPage() async {
    final currentState = state.value ?? HistoryState.initial();
    if (currentState.currentPage == 0) return;

    state = AsyncValue.data(currentState.copyWith(
      currentPage: 0,
      isLoading: true,
    ));

    await _loadEntries(state.value!);
  }

  /// Go to last page
  Future<void> goToLastPage() async {
    final currentState = state.value ?? HistoryState.initial();
    final lastPage = currentState.totalPages - 1;
    if (currentState.currentPage == lastPage) return;

    state = AsyncValue.data(currentState.copyWith(
      currentPage: lastPage,
      isLoading: true,
    ));

    await _loadEntries(state.value!);
  }

  /// Refresh data
  Future<void> refresh() async {
    final currentState = state.value ?? HistoryState.initial();
    state = AsyncValue.data(currentState.copyWith(isLoading: true));
    await _loadEntries(state.value!);
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel(); // Cancel any existing subscription
    _eventSubscription = EventBus().stream.listen((event) {
      Logger.info('HistoryProvider: Received event: $event');
      if (event == EventType.dataUpdated) {
        Logger.info(
            'HistoryProvider: Received data update event, refreshing history');
        // Refresh the history when data is updated (maintains current page/scroll position)
        refresh();
      } else if (event == EventType.averagesCalculating) {
        Logger.info(
            'HistoryProvider: Averages calculating, showing loading state');
        final currentState = state.value ?? HistoryState.initial();
        state = AsyncValue.data(currentState.copyWith(isLoading: true));
      } else if (event == EventType.averageCalculationFailed) {
        Logger.info(
            'HistoryProvider: Average calculation failed, clearing loading state');
        final currentState = state.value ?? HistoryState.initial();
        state = AsyncValue.data(currentState.copyWith(isLoading: false));
      }
    });
  }

  /// Clear error
  void clearError() {
    final currentState = state.value ?? HistoryState.initial();
    state = AsyncValue.data(currentState.copyWith(errorMessage: null));
  }

  /// Smart pagination logic that fixes the bug
  Future<void> _loadEntries(HistoryState currentState) async {
    try {
      List<dynamic> paginatedEntries = [];
      int totalCount = 0;

      // Use smart pagination strategy based on filter type
      if (currentState.filterType == EntryFilterType.all) {
        // For "All Entries" - calculate total count first, then paginate
        final result = await _loadAllEntriesWithCount(currentState);
        paginatedEntries = result['entries'];
        totalCount = result['totalCount'];
      } else {
        // For single-type filters - calculate total count first, then paginate
        final result = await _loadSingleTypeEntriesWithCount(currentState);
        paginatedEntries = result['entries'];
        totalCount = result['totalCount'];
      }

      // Calculate pagination using correct total count
      final totalPages = (totalCount / currentState.entriesPerPage).ceil();
      final showPaginationControls = totalPages > 1;

      // Update state
      state = AsyncValue.data(currentState.copyWith(
        entries: paginatedEntries,
        totalEntries: totalCount,
        totalPages: totalPages > 0 ? totalPages : 1,
        showPaginationControls: showPaginationControls,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to load history entries: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load entries: ${error.toString()}',
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'History entries loading',
          );
    }
  }

  /// Load all entries with total count and pagination
  Future<Map<String, dynamic>> _loadAllEntriesWithCount(
      HistoryState currentState) async {
    final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
    final topUpRepo = ref.read(topUpRepositoryProvider);

    // Load ALL entries first
    List<MeterReading> allMeterReadings = [];
    List<TopUp> allTopUps = [];

    if (currentState.hasDateFilter) {
      // Date range filter
      allMeterReadings = await meterReadingRepo.getMeterReadingsByDateRange(
        startDate: currentState.startDate!,
        endDate: currentState.endDate!,
      );
      allTopUps = await topUpRepo.getTopUpsByDateRange(
        startDate: currentState.startDate!,
        endDate: currentState.endDate!,
      );
    } else {
      // No date filter - load all
      allMeterReadings = await meterReadingRepo.getAllMeterReadings();
      allTopUps = await topUpRepo.getAllTopUps();
    }

    // Combine and sort all entries
    final allEntries = <dynamic>[...allMeterReadings, ...allTopUps];
    _sortEntries(allEntries, currentState.sortOrder);

    // Get total count before pagination
    final totalCount = allEntries.length;

    // Apply pagination at application level
    final startIndex = currentState.currentPage * currentState.entriesPerPage;
    final endIndex =
        (startIndex + currentState.entriesPerPage).clamp(0, allEntries.length);

    final paginatedEntries = allEntries.sublist(startIndex, endIndex);

    return {
      'entries': paginatedEntries,
      'totalCount': totalCount,
    };
  }

  /// Load single-type entries with total count and pagination
  Future<Map<String, dynamic>> _loadSingleTypeEntriesWithCount(
      HistoryState currentState) async {
    final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
    final topUpRepo = ref.read(topUpRepositoryProvider);

    List<dynamic> allEntries = [];
    List<dynamic> paginatedEntries = [];

    switch (currentState.filterType) {
      case EntryFilterType.meterReadings:
        if (currentState.hasDateFilter) {
          allEntries = await meterReadingRepo.getMeterReadingsByDateRange(
            startDate: currentState.startDate!,
            endDate: currentState.endDate!,
          );
          _sortEntries(allEntries, currentState.sortOrder);
          // Apply pagination for date range
          final startIndex =
              currentState.currentPage * currentState.entriesPerPage;
          final endIndex = (startIndex + currentState.entriesPerPage)
              .clamp(0, allEntries.length);
          paginatedEntries = allEntries.sublist(startIndex, endIndex);
        } else {
          paginatedEntries = await meterReadingRepo.getMeterReadings(
            page: currentState.currentPage,
            pageSize: currentState.entriesPerPage,
          );
          allEntries = await meterReadingRepo.getAllMeterReadings();
        }
        break;

      case EntryFilterType.topUps:
        if (currentState.hasDateFilter) {
          allEntries = await topUpRepo.getTopUpsByDateRange(
            startDate: currentState.startDate!,
            endDate: currentState.endDate!,
          );
          _sortEntries(allEntries, currentState.sortOrder);
          // Apply pagination for date range
          final startIndex =
              currentState.currentPage * currentState.entriesPerPage;
          final endIndex = (startIndex + currentState.entriesPerPage)
              .clamp(0, allEntries.length);
          paginatedEntries = allEntries.sublist(startIndex, endIndex);
        } else {
          paginatedEntries = await topUpRepo.getTopUps(
            page: currentState.currentPage,
            pageSize: currentState.entriesPerPage,
          );
          allEntries = await topUpRepo.getAllTopUps();
        }
        break;

      case EntryFilterType.invalid:
        if (currentState.hasDateFilter) {
          // Get all entries within date range, then filter for invalid ones
          final dateRangeMeterReadings =
              await meterReadingRepo.getMeterReadingsByDateRange(
            startDate: currentState.startDate!,
            endDate: currentState.endDate!,
          );
          final dateRangeTopUps = await topUpRepo.getTopUpsByDateRange(
            startDate: currentState.startDate!,
            endDate: currentState.endDate!,
          );

          final invalidMeterReadings = dateRangeMeterReadings
              .where((reading) => !reading.isValid)
              .toList();
          final invalidTopUps =
              dateRangeTopUps.where((topUp) => !topUp.isValid).toList();

          allEntries = [...invalidMeterReadings, ...invalidTopUps];
        } else {
          // No date filter - get all invalid entries
          final invalidMeterReadings =
              await meterReadingRepo.getInvalidMeterReadings();
          final allTopUps = await topUpRepo.getAllTopUps();
          final invalidTopUps =
              allTopUps.where((topUp) => !topUp.isValid).toList();

          allEntries = [...invalidMeterReadings, ...invalidTopUps];
        }

        _sortEntries(allEntries, currentState.sortOrder);

        // Apply pagination for invalid entries
        final startIndex =
            currentState.currentPage * currentState.entriesPerPage;
        final endIndex = (startIndex + currentState.entriesPerPage)
            .clamp(0, allEntries.length);
        paginatedEntries = allEntries.sublist(startIndex, endIndex);
        break;

      case EntryFilterType.all:
        // This case is handled by _loadAllEntriesWithCount
        break;
    }

    return {
      'entries': paginatedEntries,
      'totalCount': allEntries.length,
    };
  }

  /// Sort entries by date
  void _sortEntries(List<dynamic> entries, EntrySortOrder sortOrder) {
    entries.sort((a, b) {
      DateTime dateA = a is MeterReading ? a.date : (a as TopUp).date;
      DateTime dateB = b is MeterReading ? b.date : (b as TopUp).date;

      return sortOrder == EntrySortOrder.newestFirst
          ? dateB.compareTo(dateA)
          : dateA.compareTo(dateB);
    });
  }
}
