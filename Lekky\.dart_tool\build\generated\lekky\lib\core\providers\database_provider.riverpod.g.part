// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$databaseHelperHash() => r'8756a78fed012308a421b0f2a0f1b586cae71baf';

/// Database helper provider
///
/// Copied from [databaseHelper].
@ProviderFor(databaseHelper)
final databaseHelperProvider = AutoDisposeProvider<DatabaseHelper>.internal(
  databaseHelper,
  name: r'databaseHelperProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseHelperHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef DatabaseHelperRef = AutoDisposeProviderRef<DatabaseHelper>;
String _$meterReadingRepositoryHash() =>
    r'd5e3ef276804f4c7b91f81186f5dbddc1ba987c1';

/// Meter reading repository provider
///
/// Copied from [meterReadingRepository].
@ProviderFor(meterReadingRepository)
final meterReadingRepositoryProvider =
    AutoDisposeProvider<MeterReadingRepository>.internal(
  meterReadingRepository,
  name: r'meterReadingRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$meterReadingRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef MeterReadingRepositoryRef
    = AutoDisposeProviderRef<MeterReadingRepository>;
String _$topUpRepositoryHash() => r'165cb56478877413b3bf99414ed9e0b081127968';

/// Top-up repository provider
///
/// Copied from [topUpRepository].
@ProviderFor(topUpRepository)
final topUpRepositoryProvider = AutoDisposeProvider<TopUpRepository>.internal(
  topUpRepository,
  name: r'topUpRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$topUpRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef TopUpRepositoryRef = AutoDisposeProviderRef<TopUpRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
