import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../providers/welcome_provider.dart';
import '../widgets/feature_item.dart';
import '../widgets/welcome_buttons.dart';

/// The welcome screen of the app
class WelcomeScreen extends ConsumerStatefulWidget {
  /// Constructor
  const WelcomeScreen({super.key});

  @override
  ConsumerState<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends ConsumerState<WelcomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // Start the animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          const Color(0xFF003087), // Deep blue as specified in the plan
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const SizedBox(height: 40),
                          // Logo
                          Container(
                            width: 80,
                            height: 80,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.electric_meter,
                              color: Color(0xFF003087),
                              size: 48,
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Welcome Text
                          const Text(
                            'Welcome to Lekky',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          // Subtitle
                          const Text(
                            'Your personal prepaid meter assistant',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 40),
                          // Features
                          const FeatureItem(
                            icon: Icons.track_changes,
                            title: 'Track Your Usage',
                            description:
                                'Monitor your electricity consumption and spending',
                          ),
                          const SizedBox(height: 24),
                          const FeatureItem(
                            icon: Icons.notifications,
                            title: 'Get Timely Alerts',
                            description:
                                'Receive notifications when your balance is running low',
                          ),
                          const SizedBox(height: 24),
                          const FeatureItem(
                            icon: Icons.history,
                            title: 'View History',
                            description:
                                'See your past meter readings and top-ups',
                          ),
                          const SizedBox(height: 24),
                          const FeatureItem(
                            icon: Icons.attach_money,
                            title: 'Calculate Costs',
                            description:
                                'Estimate your electricity costs over different periods',
                          ),
                          const SizedBox(height: 40),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // Get Started Button
            Consumer(
              builder: (context, ref, _) {
                return GetStartedButton(
                  onPressed: () => _onGetStarted(context, ref),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _onGetStarted(BuildContext context, WidgetRef ref) {
    // Mark welcome as completed and then navigate
    ref.read(welcomeProvider.notifier).markWelcomeCompleted().then((_) {
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(AppConstants.routeSetup);
      }
    });
  }
}
